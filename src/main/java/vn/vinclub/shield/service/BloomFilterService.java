package vn.vinclub.shield.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.shield.enums.Action;
import vn.vinclub.shield.enums.TimePeriod;
import vn.vinclub.shield.enums.TrackingDataType;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Centralized bloom filter management for all tracking operations
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BloomFilterService {

    private final RedissonClient redissonClient;

    // Configuration
    @Value("${shield.bloom.filter.global.size:2000000}")
    private Long globalFilterSize;

    @Value("${shield.bloom.filter.monthly.size:1000000}")
    private Long monthlyFilterSize;

    @Value("${shield.bloom.filter.daily.size:500000}")
    private Long dailyFilterSize;

    @Value("${shield.bloom.filter.false-positive-rate:0.01}")
    private Double falsePositiveRate;

    // Local cache for bloom filter existence checks
    private Cache<String, Boolean> filterExistenceCache;

    // Local cache for bloom filter instances (short-lived)
    private Cache<String, RBloomFilter<String>> filterInstanceCache;

    @PostConstruct
    public void initializeCaches() {
        // Cache for filter existence - longer TTL since filters don't get deleted often
        filterExistenceCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofMinutes(5))
                .build();

        // Cache for filter instances - shorter TTL to avoid memory issues
        filterInstanceCache = Caffeine.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(Duration.ofMinutes(1))
                .build();
    }

    /**
     * Check if an identifier exists without adding it
     */
    @Profiler
    public boolean containsIdentifier(String identifier, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (!StringUtils.hasText(identifier)) {
            return false;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

        if (!filter.isExists()) {
            return false;
        }

        return filter.contains(identifier);
    }

    @Profiler
    public boolean notContainsIdentifier(String identifier, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (!StringUtils.hasText(identifier)) {
            return false;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

        if (!filter.isExists()) {
            return true;
        }

        return !filter.contains(identifier);
    }

    @Profiler
    public boolean notContainsAnyIdentifier(List<String> identifiers, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (CollectionUtils.isEmpty(identifiers)) {
            return false;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

        if (!filter.isExists()) {
            return true;
        }
        for (String identifier : identifiers) {
            if (StringUtils.hasText(identifier)) {
                if (filter.contains(identifier)) {
                    return false;
                }
            }
        }
        return true;
    }

    @Profiler
    public void addIdentifier(String identifier, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (!StringUtils.hasText(identifier)) {
            return;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

        filter.addAsync(identifier);
        log.debug("[ADD] filter={}, identifier={}", filterKey, identifier);
    }

    @Profiler
    public void addIdentifiers(List<String> identifiers, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (CollectionUtils.isEmpty(identifiers)) {
            return;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);
        filter.addAsync(identifiers);
    }

    /**
     * Batch check multiple identifiers across different time periods for a single data type
     * This significantly reduces Redis round trips by batching operations
     */
    @Profiler
    public Map<TimePeriod, Boolean> batchCheckIdentifier(String identifier, Action action, TrackingDataType dataType, List<TimePeriod> timePeriods) {
        if (!StringUtils.hasText(identifier) || CollectionUtils.isEmpty(timePeriods)) {
            return new HashMap<>();
        }

        Map<TimePeriod, CompletableFuture<Boolean>> futures = new HashMap<>();

        // Start all checks in parallel
        for (TimePeriod timePeriod : timePeriods) {
            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                String filterKey = buildFilterKey(action, dataType, timePeriod);
                RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

                if (!filter.isExists()) {
                    return false;
                }
                return filter.contains(identifier);
            });
            futures.put(timePeriod, future);
        }

        // Collect all results
        Map<TimePeriod, Boolean> results = new HashMap<>();
        for (Map.Entry<TimePeriod, CompletableFuture<Boolean>> entry : futures.entrySet()) {
            try {
                results.put(entry.getKey(), entry.getValue().get());
            } catch (Exception e) {
                log.warn("Error checking identifier {} for period {}: {}", identifier, entry.getKey(), e.getMessage());
                results.put(entry.getKey(), false);
            }
        }

        return results;
    }

    /**
     * Batch check multiple identifiers across different time periods for a single data type
     * Returns true if NONE of the identifiers are found (all are new)
     */
    @Profiler
    public Map<TimePeriod, Boolean> batchCheckIdentifiersNotContained(List<String> identifiers, Action action, TrackingDataType dataType, List<TimePeriod> timePeriods) {
        if (CollectionUtils.isEmpty(identifiers) || CollectionUtils.isEmpty(timePeriods)) {
            return new HashMap<>();
        }

        Map<TimePeriod, CompletableFuture<Boolean>> futures = new HashMap<>();

        // Start all checks in parallel
        for (TimePeriod timePeriod : timePeriods) {
            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                String filterKey = buildFilterKey(action, dataType, timePeriod);
                RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

                if (!filter.isExists()) {
                    return true; // Filter doesn't exist, so none are contained
                }

                // Check if any identifier is contained
                for (String identifier : identifiers) {
                    if (StringUtils.hasText(identifier) && filter.contains(identifier)) {
                        return false; // Found one, so not all are new
                    }
                }
                return true; // None found, all are new
            });
            futures.put(timePeriod, future);
        }

        // Collect all results
        Map<TimePeriod, Boolean> results = new HashMap<>();
        for (Map.Entry<TimePeriod, CompletableFuture<Boolean>> entry : futures.entrySet()) {
            try {
                results.put(entry.getKey(), entry.getValue().get());
            } catch (Exception e) {
                log.warn("Error checking identifiers {} for period {}: {}", identifiers, entry.getKey(), e.getMessage());
                results.put(entry.getKey(), true); // Default to "not contained" on error
            }
        }

        return results;
    }

    /**
     * Batch check for cross-correlation analysis - checks multiple identifiers of different types
     * Returns a map of identifier -> whether it exists in ALL_TIME period
     */
    @Profiler
    public Map<String, Boolean> batchCheckCrossCorrelationIdentifiers(Map<String, TrackingDataType> identifierTypeMap) {
        if (CollectionUtils.isEmpty(identifierTypeMap)) {
            return new HashMap<>();
        }

        Map<String, CompletableFuture<Boolean>> futures = new HashMap<>();

        // Start all checks in parallel
        for (Map.Entry<String, TrackingDataType> entry : identifierTypeMap.entrySet()) {
            String identifier = entry.getKey();
            TrackingDataType dataType = entry.getValue();

            if (!StringUtils.hasText(identifier)) {
                continue;
            }

            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                String filterKey = buildFilterKey(null, dataType, TimePeriod.ALL_TIME);
                RBloomFilter<String> filter = getOrCreateFilter(filterKey, TimePeriod.ALL_TIME);

                if (!filter.isExists()) {
                    return false;
                }
                return filter.contains(identifier);
            });
            futures.put(identifier, future);
        }

        // Collect all results
        Map<String, Boolean> results = new HashMap<>();
        for (Map.Entry<String, CompletableFuture<Boolean>> entry : futures.entrySet()) {
            try {
                results.put(entry.getKey(), entry.getValue().get());
            } catch (Exception e) {
                log.warn("Error checking cross-correlation identifier {}: {}", entry.getKey(), e.getMessage());
                results.put(entry.getKey(), false);
            }
        }

        return results;
    }

    /**
     * Get or create a bloom filter with appropriate configuration - OPTIMIZED WITH CACHING
     */
    private RBloomFilter<String> getOrCreateFilter(String filterKey, TimePeriod timePeriod) {
        // Try to get from instance cache first
        RBloomFilter<String> cachedFilter = filterInstanceCache.getIfPresent(filterKey);
        if (cachedFilter != null) {
            return cachedFilter;
        }

        RBloomFilter<String> filter = redissonClient.getBloomFilter(filterKey);

        // Check existence from cache first
        Boolean exists = filterExistenceCache.getIfPresent(filterKey);
        if (exists == null) {
            exists = filter.isExists() && filter.getSize() > 0;
            filterExistenceCache.put(filterKey, exists);
        }

        if (!exists) {
            Long filterSize = getFilterSize(timePeriod);
            filter.tryInit(filterSize, falsePositiveRate);

            // Set TTL for time-based filters
            if (!TimePeriod.ALL_TIME.equals(timePeriod)) {
                Duration ttl = calculateTtl(timePeriod);
                filter.expire(ttl);
            }

            // Update cache
            filterExistenceCache.put(filterKey, true);

            log.info("[CREATED] filterKey={}, filterSize={}, falsePositiveRate={}",
                    filterKey, filterSize, falsePositiveRate);
        }

        // Cache the filter instance for short term
        filterInstanceCache.put(filterKey, filter);

        return filter;
    }

    /**
     * Build a standardized filter key
     */
    private String buildFilterKey(Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        StringBuilder keyBuilder = new StringBuilder("shield_svc_filter_");
        keyBuilder.append(dataType.getKeyPrefix());

        if (!TimePeriod.ALL_TIME.equals(timePeriod)) {
            keyBuilder.append("_").append(timePeriod.getKeyPrefix());
            String timeSuffix = timePeriod.getTimeSuffix();
            if (!timeSuffix.isEmpty()) {
                keyBuilder.append(":").append(timeSuffix);
            }
        }

        if (action != null) {
            keyBuilder.append(":").append(action.name());
        }

        return keyBuilder.toString();
    }

    /**
     * Get the appropriate filter size based on time period
     */
    private Long getFilterSize(TimePeriod timePeriod) {
        return switch (timePeriod) {
            case ALL_TIME -> globalFilterSize;
            case MONTHLY -> monthlyFilterSize;
            case DAILY -> dailyFilterSize;
            default -> 1000000L;
        };
    }

    /**
     * Calculate TTL for time-based filters
     */
    private Duration calculateTtl(TimePeriod timePeriod) {
        LocalDateTime now = LocalDateTime.now();

        switch (timePeriod) {
            case MONTHLY:
                LocalDate nextMonth = now.toLocalDate().plusMonths(1).withDayOfMonth(1);
                LocalDateTime endOfNextMonth = LocalDateTime.of(nextMonth.withDayOfMonth(nextMonth.lengthOfMonth()), LocalTime.MAX);
                return Duration.between(now, endOfNextMonth);

            case DAILY:
                LocalDateTime endOfTomorrow = LocalDateTime.of(now.toLocalDate().plusDays(1), LocalTime.MAX);
                return Duration.between(now, endOfTomorrow);

            default:
                return Duration.ofDays(1); // Default fallback
        }
    }
}
