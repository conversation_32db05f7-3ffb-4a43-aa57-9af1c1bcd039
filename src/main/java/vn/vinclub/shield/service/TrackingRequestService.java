package vn.vinclub.shield.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.shield.dto.TrackingContext;
import vn.vinclub.shield.dto.TrackingResults;
import vn.vinclub.shield.enums.TimePeriod;
import vn.vinclub.shield.enums.TrackingDataType;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

/**
 * Service for comprehensive tracking data analysis
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrackingRequestService {
    private final BloomFilterService bloomFilterService;
    private final RequestCounterService requestCounterService;

    /**
     * Perform comprehensive tracking analysis for the given context
     * Optimized with parallel processing and batch operations
     */
    @Profiler
    public TrackingResults analyzeTrackingRequest(TrackingContext context) {
        log.debug("[START] action={}, actionKey={}, ip={}, customers={}, devices={}",
                context.getAction(),
                context.getActionKey(),
                context.getIpAddress(),
                context.getCustomerIdentifiers(),
                context.getDeviceIdentifiers()
        );

        TrackingResults.TrackingResultsBuilder resultsBuilder = TrackingResults.builder();

        try {
            // Execute all independent analysis operations in parallel
            CompletableFuture<Void> ipTrackingFuture = CompletableFuture.runAsync(() ->
                analyzeIpTrackingOptimized(context, resultsBuilder));

            CompletableFuture<Void> customerTrackingFuture = CompletableFuture.runAsync(() ->
                analyzeCustomerTrackingOptimized(context, resultsBuilder));

            CompletableFuture<Void> deviceTrackingFuture = CompletableFuture.runAsync(() ->
                analyzeDeviceTrackingOptimized(context, resultsBuilder));

            // Wait for all basic tracking analysis to complete
            CompletableFuture.allOf(ipTrackingFuture, customerTrackingFuture, deviceTrackingFuture).join();

            // Execute cross-correlation analysis (depends on basic tracking results)
            CompletableFuture<Void> crossCorrelationFuture = CompletableFuture.runAsync(() ->
                analyzeCrossCorrelationPatternsOptimized(context, resultsBuilder));

            // Execute velocity and temporal analysis in parallel with cross-correlation
            CompletableFuture<Void> velocityFuture = CompletableFuture.runAsync(() ->
                analyzeVelocityPatterns(context, resultsBuilder));

            CompletableFuture<Void> temporalFuture = CompletableFuture.runAsync(() ->
                analyzeTemporalPatterns(context, resultsBuilder));

            // Wait for all analysis to complete
            CompletableFuture.allOf(crossCorrelationFuture, velocityFuture, temporalFuture).join();

        } catch (CompletionException e) {
            log.error("Error during parallel tracking analysis", e);
            // Fallback to sequential processing if parallel fails
            analyzeIpTracking(context, resultsBuilder);
            analyzeCustomerTracking(context, resultsBuilder);
            analyzeDeviceTracking(context, resultsBuilder);
            analyzeCrossCorrelationPatterns(context, resultsBuilder);
        }

        TrackingResults results = resultsBuilder.build();
        log.debug("[COMPLETE] action={}, actionKey={}, ip={}, customerIds={}, deviceIds={} => {}",
                context.getAction(),
                context.getActionKey(),
                context.getIpAddress(),
                context.getCustomerIdentifiers(),
                context.getDeviceIdentifiers(),
                results
        );

        return results;
    }

    @Profiler
    public void putTrackingRequestData(TrackingContext context) {
        if (context.hasIpAddress()) {
            bloomFilterService.addIdentifier(context.getIpAddress(), null, TrackingDataType.IP_ADDRESS, TimePeriod.ALL_TIME);
            bloomFilterService.addIdentifier(context.getIpAddress(), null, TrackingDataType.IP_ADDRESS, TimePeriod.MONTHLY);
            bloomFilterService.addIdentifier(context.getIpAddress(), null, TrackingDataType.IP_ADDRESS, TimePeriod.DAILY);
            bloomFilterService.addIdentifier(context.getIpAddress(), context.getAction(), TrackingDataType.IP_ADDRESS, TimePeriod.DAILY);
        }

        if (context.hasCustomerData()) {
            bloomFilterService.addIdentifiers(context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.ALL_TIME);
            bloomFilterService.addIdentifiers(context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.MONTHLY);
            bloomFilterService.addIdentifiers(context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.DAILY);
            bloomFilterService.addIdentifiers(context.getCustomerIdentifiers(), context.getAction(), TrackingDataType.CUSTOMER_DATA, TimePeriod.DAILY);
        }

        if (context.hasDeviceData()) {
            bloomFilterService.addIdentifiers(context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.ALL_TIME);
            bloomFilterService.addIdentifiers(context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.MONTHLY);
            bloomFilterService.addIdentifiers(context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.DAILY);
            bloomFilterService.addIdentifiers(context.getDeviceIdentifiers(), context.getAction(), TrackingDataType.DEVICE_DATA, TimePeriod.DAILY);
        }
    }

    /**
     * Analyze IP address tracking across all time periods - OPTIMIZED VERSION
     */
    private void analyzeIpTrackingOptimized(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        if (!context.hasIpAddress()) {
            return;
        }

        String ipAddress = context.getIpAddress();
        List<TimePeriod> timePeriods = Arrays.asList(TimePeriod.ALL_TIME, TimePeriod.MONTHLY, TimePeriod.DAILY);

        // Batch check for all time periods without action
        Map<TimePeriod, Boolean> generalResults = bloomFilterService.batchCheckIdentifier(
                ipAddress, null, TrackingDataType.IP_ADDRESS, timePeriods);

        // Single check for action-specific daily tracking
        boolean ipAddressFirstActionToday = bloomFilterService.notContainsIdentifier(
                ipAddress, context.getAction(), TrackingDataType.IP_ADDRESS, TimePeriod.DAILY);

        // Set results (note: batchCheckIdentifier returns containment, we need "first time seen" which is NOT contained)
        resultsBuilder.ipAddressFirstTimeSeen(!generalResults.getOrDefault(TimePeriod.ALL_TIME, false));
        resultsBuilder.ipAddressFirstThisMonth(!generalResults.getOrDefault(TimePeriod.MONTHLY, false));
        resultsBuilder.ipAddressFirstToday(!generalResults.getOrDefault(TimePeriod.DAILY, false));
        resultsBuilder.ipAddressFirstActionToday(ipAddressFirstActionToday);
    }

    /**
     * Analyze IP address tracking across all time periods - ORIGINAL VERSION (fallback)
     */
    private void analyzeIpTracking(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        if (!context.hasIpAddress()) {
            return;
        }

        String ipAddress = context.getIpAddress();

        // All time IP tracking
        boolean ipAddressFirstTimeSeen = bloomFilterService.notContainsIdentifier(
                ipAddress, null, TrackingDataType.IP_ADDRESS, TimePeriod.ALL_TIME);
        resultsBuilder.ipAddressFirstTimeSeen(ipAddressFirstTimeSeen);

        // Monthly IP tracking
        boolean ipAddressFirstThisMonth = bloomFilterService.notContainsIdentifier(
                ipAddress, null, TrackingDataType.IP_ADDRESS, TimePeriod.MONTHLY);
        resultsBuilder.ipAddressFirstThisMonth(ipAddressFirstThisMonth);

        // Daily IP tracking
        boolean ipAddressFirstToday = bloomFilterService.notContainsIdentifier(
                ipAddress, null, TrackingDataType.IP_ADDRESS, TimePeriod.DAILY);
        resultsBuilder.ipAddressFirstToday(ipAddressFirstToday);

        // First action by IP today
        boolean ipAddressFirstActionToday = bloomFilterService.notContainsIdentifier(
                ipAddress, context.getAction(), TrackingDataType.IP_ADDRESS, TimePeriod.DAILY);
        resultsBuilder.ipAddressFirstActionToday(ipAddressFirstActionToday);
    }


    /**
     * Analyze customer data tracking across time periods - OPTIMIZED VERSION
     */
    private void analyzeCustomerTrackingOptimized(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        if (!context.hasCustomerData()) {
            return;
        }

        List<TimePeriod> timePeriods = Arrays.asList(TimePeriod.ALL_TIME, TimePeriod.MONTHLY, TimePeriod.DAILY);

        // Batch check for all time periods without action
        Map<TimePeriod, Boolean> generalResults = bloomFilterService.batchCheckIdentifiersNotContained(
                context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, timePeriods);

        // Single check for action-specific daily tracking
        boolean userIdentityFirstActionToday = bloomFilterService.notContainsAnyIdentifier(
                context.getCustomerIdentifiers(), context.getAction(), TrackingDataType.CUSTOMER_DATA, TimePeriod.DAILY);

        // Set results (batchCheckIdentifiersNotContained already returns "not contained" boolean)
        resultsBuilder.userIdentityFirstTimeSeen(generalResults.getOrDefault(TimePeriod.ALL_TIME, true));
        resultsBuilder.userIdentityFirstThisMonth(generalResults.getOrDefault(TimePeriod.MONTHLY, true));
        resultsBuilder.userIdentityFirstToday(generalResults.getOrDefault(TimePeriod.DAILY, true));
        resultsBuilder.userIdentityFirstActionToday(userIdentityFirstActionToday);
    }

    /**
     * Analyze customer data tracking across time periods - ORIGINAL VERSION (fallback)
     */
    private void analyzeCustomerTracking(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        if (!context.hasCustomerData()) {
            return;
        }

        // Customer data tracking
        boolean userIdentityFirstTimeSeen = bloomFilterService.notContainsAnyIdentifier(
                context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.ALL_TIME);
        resultsBuilder.userIdentityFirstTimeSeen(userIdentityFirstTimeSeen);

        // Monthly customer tracking
        boolean userIdentityFirstThisMonth = bloomFilterService.notContainsAnyIdentifier(
                context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.MONTHLY);
        resultsBuilder.userIdentityFirstThisMonth(userIdentityFirstThisMonth);

        // Daily customer tracking
        boolean userIdentityFirstToday = bloomFilterService.notContainsAnyIdentifier(
                context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.DAILY);
        resultsBuilder.userIdentityFirstToday(userIdentityFirstToday);

        // First action by customer today
        boolean userIdentityFirstActionToday = bloomFilterService.notContainsAnyIdentifier(
                context.getCustomerIdentifiers(), context.getAction(), TrackingDataType.CUSTOMER_DATA, TimePeriod.DAILY);
        resultsBuilder.userIdentityFirstActionToday(userIdentityFirstActionToday);
    }

    /**
     * Analyze device data tracking across time periods - OPTIMIZED VERSION
     */
    private void analyzeDeviceTrackingOptimized(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        if (!context.hasDeviceData()) {
            return;
        }

        List<TimePeriod> timePeriods = Arrays.asList(TimePeriod.ALL_TIME, TimePeriod.MONTHLY, TimePeriod.DAILY);

        // Batch check for all time periods without action
        Map<TimePeriod, Boolean> generalResults = bloomFilterService.batchCheckIdentifiersNotContained(
                context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, timePeriods);

        // Single check for action-specific daily tracking
        boolean deviceFingerprintFirstActionToday = bloomFilterService.notContainsAnyIdentifier(
                context.getDeviceIdentifiers(), context.getAction(), TrackingDataType.DEVICE_DATA, TimePeriod.DAILY);

        // Set results (batchCheckIdentifiersNotContained already returns "not contained" boolean)
        resultsBuilder.deviceFingerprintFirstTimeSeen(generalResults.getOrDefault(TimePeriod.ALL_TIME, true));
        resultsBuilder.deviceFingerprintFirstThisMonth(generalResults.getOrDefault(TimePeriod.MONTHLY, true));
        resultsBuilder.deviceFingerprintFirstToday(generalResults.getOrDefault(TimePeriod.DAILY, true));
        resultsBuilder.deviceFingerprintFirstActionToday(deviceFingerprintFirstActionToday);
    }

    /**
     * Analyze device data tracking across time periods - ORIGINAL VERSION (fallback)
     */
    private void analyzeDeviceTracking(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        if (!context.hasDeviceData()) {
            return;
        }

        // Device data tracking
        boolean deviceFingerprintFirstTimeSeen = bloomFilterService.notContainsAnyIdentifier(
                context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.ALL_TIME);
        resultsBuilder.deviceFingerprintFirstTimeSeen(deviceFingerprintFirstTimeSeen);

        // Monthly device tracking
        boolean deviceFingerprintFirstThisMonth = bloomFilterService.notContainsAnyIdentifier(
                context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.MONTHLY);
        resultsBuilder.deviceFingerprintFirstThisMonth(deviceFingerprintFirstThisMonth);

        // Daily device tracking
        boolean deviceFingerprintFirstToday = bloomFilterService.notContainsAnyIdentifier(
                context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.DAILY);
        resultsBuilder.deviceFingerprintFirstToday(deviceFingerprintFirstToday);

        // First action by device today
        boolean deviceFingerprintFirstActionToday = bloomFilterService.notContainsAnyIdentifier(
                context.getDeviceIdentifiers(), context.getAction(), TrackingDataType.DEVICE_DATA, TimePeriod.DAILY);
        resultsBuilder.deviceFingerprintFirstActionToday(deviceFingerprintFirstActionToday);
    }

    /**
     * Analyze cross-correlation patterns between different data types - OPTIMIZED VERSION
     * This helps identify behavioral anomalies and suspicious patterns
     */
    private void analyzeCrossCorrelationPatternsOptimized(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        analyzeBehavioralAnomaliesOptimized(context, resultsBuilder);
    }

    /**
     * Analyze cross-correlation patterns between different data types - ORIGINAL VERSION (fallback)
     * This helps identify behavioral anomalies and suspicious patterns
     */
    private void analyzeCrossCorrelationPatterns(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        analyzeBehavioralAnomalies(context, resultsBuilder);

        analyzeVelocityPatterns(context, resultsBuilder);

        analyzeTemporalPatterns(context, resultsBuilder);
    }

    /**
     * Analyze behavioral anomalies through cross-correlation - OPTIMIZED VERSION
     */
    private void analyzeBehavioralAnomaliesOptimized(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        // Prepare batch check for all identifiers
        Map<String, TrackingDataType> identifierTypeMap = new HashMap<>();

        if (context.hasIpAddress()) {
            identifierTypeMap.put(context.getIpAddress(), TrackingDataType.IP_ADDRESS);
        }

        if (context.hasCustomerData()) {
            for (String customerId : context.getCustomerIdentifiers()) {
                if (customerId != null && !customerId.trim().isEmpty()) {
                    identifierTypeMap.put(customerId, TrackingDataType.CUSTOMER_DATA);
                }
            }
        }

        if (context.hasDeviceData()) {
            for (String deviceId : context.getDeviceIdentifiers()) {
                if (deviceId != null && !deviceId.trim().isEmpty()) {
                    identifierTypeMap.put(deviceId, TrackingDataType.DEVICE_DATA);
                }
            }
        }

        // Single batch call to check all identifiers
        Map<String, Boolean> knownIdentifiers = bloomFilterService.batchCheckCrossCorrelationIdentifiers(identifierTypeMap);

        // Analyze results
        boolean ipKnownGlobally = false;
        boolean userKnownGlobally = false;
        boolean deviceKnownGlobally = false;

        if (context.hasIpAddress()) {
            ipKnownGlobally = knownIdentifiers.getOrDefault(context.getIpAddress(), false);
        }

        if (context.hasCustomerData()) {
            for (String customerId : context.getCustomerIdentifiers()) {
                if (knownIdentifiers.getOrDefault(customerId, false)) {
                    userKnownGlobally = true;
                    break;
                }
            }
        }

        if (context.hasDeviceData()) {
            for (String deviceId : context.getDeviceIdentifiers()) {
                if (knownIdentifiers.getOrDefault(deviceId, false)) {
                    deviceKnownGlobally = true;
                    break;
                }
            }
        }

        // Calculate cross-correlation results
        boolean newUserFromKnownNetwork = context.hasIpAddress() && context.hasCustomerData() &&
                !userKnownGlobally && ipKnownGlobally;
        boolean knownUserFromNewNetwork = context.hasIpAddress() && context.hasCustomerData() &&
                userKnownGlobally && !ipKnownGlobally;
        boolean newDeviceFromKnownNetwork = context.hasIpAddress() && context.hasDeviceData() &&
                !deviceKnownGlobally && ipKnownGlobally;
        boolean knownDeviceFromNewNetwork = context.hasIpAddress() && context.hasDeviceData() &&
                deviceKnownGlobally && !ipKnownGlobally;
        boolean newUserFromKnownDevice = context.hasCustomerData() && context.hasDeviceData() &&
                !userKnownGlobally && deviceKnownGlobally;
        boolean knownUserFromNewDevice = context.hasCustomerData() && context.hasDeviceData() &&
                userKnownGlobally && !deviceKnownGlobally;

        resultsBuilder.behaviorNewUserFromKnownNetwork(newUserFromKnownNetwork);
        resultsBuilder.behaviorKnownUserFromNewNetwork(knownUserFromNewNetwork);
        resultsBuilder.behaviorNewDeviceFromKnownNetwork(newDeviceFromKnownNetwork);
        resultsBuilder.behaviorKnownDeviceFromNewNetwork(knownDeviceFromNewNetwork);
        resultsBuilder.behaviorNewUserFromKnownDevice(newUserFromKnownDevice);
        resultsBuilder.behaviorKnownUserFromNewDevice(knownUserFromNewDevice);
    }

    /**
     * Analyze behavioral anomalies through cross-correlation - ORIGINAL VERSION (fallback)
     */
    private void analyzeBehavioralAnomalies(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        boolean newUserFromKnownNetwork = false;
        boolean knownUserFromNewNetwork = false;
        boolean newDeviceFromKnownNetwork = false;
        boolean knownDeviceFromNewNetwork = false;

        // Check if we have the necessary data for cross-correlation
        if (context.hasIpAddress()) {
            String ipAddress = context.getIpAddress();

            boolean ipKnownGlobally = bloomFilterService.containsIdentifier(
                    ipAddress, null, TrackingDataType.IP_ADDRESS, TimePeriod.ALL_TIME);

            // Analyze user-network correlation
            if (context.hasCustomerData()) {
                boolean userKnownGlobally = false;
                for (String customerId : context.getCustomerIdentifiers()) {
                    boolean userKnown = bloomFilterService.containsIdentifier(
                            customerId, null, TrackingDataType.CUSTOMER_DATA, TimePeriod.ALL_TIME);

                    if (userKnown) {
                        userKnownGlobally = true;
                        break;
                    }
                }

                // Cross-correlation analysis
                newUserFromKnownNetwork = !userKnownGlobally && ipKnownGlobally;
                knownUserFromNewNetwork = userKnownGlobally && !ipKnownGlobally;
            }

            // Analyze device-network correlation
            if (context.hasDeviceData()) {
                boolean deviceKnownGlobally = false;
                for (String deviceId : context.getDeviceIdentifiers()) {
                    boolean deviceKnown = bloomFilterService.containsIdentifier(
                            deviceId, null, TrackingDataType.DEVICE_DATA, TimePeriod.ALL_TIME);

                    if (deviceKnown) {
                        deviceKnownGlobally = true;
                        break;
                    }
                }

                // Cross-correlation analysis
                newDeviceFromKnownNetwork = !deviceKnownGlobally && ipKnownGlobally;
                knownDeviceFromNewNetwork = deviceKnownGlobally && !ipKnownGlobally;
            }
        }

        // Analyze user-device correlation
        boolean newUserFromKnownDevice = false;
        boolean knownUserFromNewDevice = false;

        if (context.hasCustomerData() && context.hasDeviceData()) {
            boolean userKnownGlobally = false;
            boolean deviceKnownGlobally = false;

            // Check if user is known
            for (String customerId : context.getCustomerIdentifiers()) {
                boolean userKnown = bloomFilterService.containsIdentifier(
                        customerId, null, TrackingDataType.CUSTOMER_DATA, TimePeriod.ALL_TIME);

                if (userKnown) {
                    userKnownGlobally = true;
                    break;
                }
            }

            // Check if device is known
            for (String deviceId : context.getDeviceIdentifiers()) {
                boolean deviceKnown = bloomFilterService.containsIdentifier(
                        deviceId, null, TrackingDataType.DEVICE_DATA, TimePeriod.ALL_TIME);
                if (deviceKnown) {
                    deviceKnownGlobally = true;
                    break;
                }
            }

            // User-device correlation analysis
            newUserFromKnownDevice = !userKnownGlobally && deviceKnownGlobally;
            knownUserFromNewDevice = userKnownGlobally && !deviceKnownGlobally;
        }

        resultsBuilder.behaviorNewUserFromKnownNetwork(newUserFromKnownNetwork);
        resultsBuilder.behaviorKnownUserFromNewNetwork(knownUserFromNewNetwork);
        resultsBuilder.behaviorNewDeviceFromKnownNetwork(newDeviceFromKnownNetwork);
        resultsBuilder.behaviorKnownDeviceFromNewNetwork(knownDeviceFromNewNetwork);
        resultsBuilder.behaviorNewUserFromKnownDevice(newUserFromKnownDevice);
        resultsBuilder.behaviorKnownUserFromNewDevice(knownUserFromNewDevice);
    }

    /**
     * Analyze velocity and frequency patterns
     */
    private void analyzeVelocityPatterns(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        boolean multipleNewIndicators = false;
        boolean highFrequencyFromNetwork = false;
        boolean unusualUserActivity = false;

        if (context.hasIpAddress()) {
            String sourceIdentifier = context.getIpAddress();

            if (context.hasCustomerData()) {
                multipleNewIndicators = requestCounterService.checkMultipleNewIndicators(
                        sourceIdentifier, context.getAction(), "customer");
            }
            if (context.hasDeviceData()) {
                multipleNewIndicators = multipleNewIndicators || requestCounterService.checkMultipleNewIndicators(
                        sourceIdentifier, context.getAction(), "device");
            }


            highFrequencyFromNetwork = requestCounterService.checkAndIncrementVelocityCounter(
                    context.getIpAddress(), context.getAction());
        }

        if (context.hasCustomerData()) {
            for (String customerId : context.getCustomerIdentifiers()) {
                if (requestCounterService.checkUnusualUserActivity(customerId, context.getAction())) {
                    unusualUserActivity = true;
                    break;
                }
            }
        }

        resultsBuilder.velocityMultipleNewIndicatorsDetected(multipleNewIndicators);
        resultsBuilder.velocityHighFrequencyFromNetwork(highFrequencyFromNetwork);
        resultsBuilder.velocityUnusualUserActivityPattern(unusualUserActivity);
    }

    /**
     * Analyze temporal patterns for suspicious timing
     */
    private void analyzeTemporalPatterns(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        boolean offHoursActivity = requestCounterService.isOffHoursActivity();

        boolean automatedBehavior = false;
        boolean rapidFirePattern = false;

        if (context.hasIpAddress()) {
            automatedBehavior = requestCounterService.checkAutomatedBehaviorPattern(
                    context.getIpAddress(), context.getAction());

            rapidFirePattern = requestCounterService.checkRapidFirePattern(
                    context.getIpAddress(), context.getAction());
        }

        resultsBuilder.temporalOffHoursActivityDetected(offHoursActivity);
        resultsBuilder.temporalAutomatedBehaviorPattern(automatedBehavior);
        resultsBuilder.temporalRapidFireRequestPattern(rapidFirePattern);
    }
}
